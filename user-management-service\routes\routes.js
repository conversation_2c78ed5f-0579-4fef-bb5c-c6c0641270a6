const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');
const multer = require('multer');
const { client } = require('../cache');
const userControllers = require('../controllers/users');
const contractorController = require('../controllers/contractor');

const { Cloudinary } = require('../cloudinary');

const upload = multer({ storage: Cloudinary.getStorage() });

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

router.get('/', doAuthenticate, (req, res) => {
    const { user } = req;
    res.status(200).json(user);
});

router.post('/login', catchAsync(userControllers.login));

router.post(
    '/signup',
    upload.single('avatar'),
    catchAsync(userControllers.signup)
);

router.post('/refresh', catchAsync(userControllers));

router.post('contractor/signup', doAuthenticate, contractorController.signup);

router.get(
    '/profile',
    doAuthenticate,
    contractorController.getContractorProfile
);
router.put(
    '/update',
    doAuthenticate,
    contractorController.updateContractorProfile
);
router.get(
    '/requests',
    doAuthenticate,
    contractorController.getServiceRequests
);
router.post(
    '/requests/:requestId/respond',
    doAuthenticate,
    contractorController.respondToRequest
);
router.get('/bookings', doAuthenticate, contractorController.getBookings);

module.exports = router;
