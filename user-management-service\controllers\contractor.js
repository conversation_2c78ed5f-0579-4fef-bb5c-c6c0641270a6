const ExpressError = require('@build-connect/utils/ExpressError');
const Contractor = require('../model/contractor');
const ServiceRequest = require('../model/serviceRequest');
const Booking = require('../model/booking');
const User = require('../model/user');
const { client } = require('../cache');

// Register a new contractor (includes location)
exports.signup = async (req, res) => {
    const { email, location, expertise, pricing } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
        throw new ExpressError(
            'User not found. Please sign up as a user first.',
            404
        );
    }

    if (user.role !== 'user') {
        throw new ExpressError(
            `User is already assigned a role: ${user.role}`,
            400
        );
    }

    user.role = 'contractor';
    try {
        await user.save();
    } catch (error) {
        throw new ExpressError(
            `Failed to update user role: ${error.message}`,
            500
        );
    }

    const existingContractor = await Contractor.findOne({ user_id: user._id });
    if (existingContractor) {
        throw new ExpressError(
            'Contractor profile already exists for this user',
            400
        );
    }

    // Create a new Contractor entry in MongoDB, including the location field
    const contractor = new Contractor({
        user_id: user._id,
        location,
        expertise,
        pricing,
    });
    try {
        await contractor.save();
    } catch (error) {
        // Revert the user's role if contractor creation fails
        user.role = 'user';
        await user.save();
        throw new ExpressError(
            `Failed to create contractor profile: ${error.message}`,
            500
        );
    }

    res.status(200).json({
        message: 'Successfully registered as a contractor!',
    });
};

// Get contractor profile
exports.getContractorProfile = async (req, res) => {
    try {
        const cacheKey = `contractor:${req.user.id}`;
        const cachedProfile = await client.get(cacheKey);

        if (cachedProfile) {
            return res.json(JSON.parse(cachedProfile));
        }

        const contractor = await Contractor.findOne({
            user_id: req.user.id,
        }).populate('user_id');
        if (!contractor) throw new ExpressError('Contractor not found', 404);

        const profile = {
            user: {
                id: contractor.user_id._id,
                name: contractor.user_id.name,
                email: contractor.user_id.email,
                phone: contractor.user_id.phone,
                role: contractor.user_id.role,
            },
            location: contractor.location,
            expertise: contractor.expertise,
            pricing: contractor.pricing,
            verification_status: contractor.verification_status,
        };

        await client.setEx(cacheKey, 3600, JSON.stringify(profile));
        res.json(profile);
    } catch (error) {
        throw new ExpressError(
            `Failed to fetch profile: ${error.message}`,
            500
        );
    }
};

// Update contractor services, expertise, pricing, and location (updates MongoDB, invalidates Redis cache)
exports.updateContractorProfile = async (req, res) => {
    const { location, expertise, pricing } = req.body;
    try {
        const contractor = await Contractor.findOneAndUpdate(
            { user_id: req.user.id },
            { location, expertise, pricing },
            { new: true }
        ).populate('user_id');
        if (!contractor) throw new ExpressError('Contractor not found', 404);

        const cacheKey = `contractor:${req.user.id}`;
        await client.del(cacheKey);

        const profile = {
            user: {
                id: contractor.user_id._id,
                name: contractor.user_id.name,
                email: contractor.user_id.email,
                phone: contractor.user_id.phone,
                role: contractor.user_id.role,
            },
            location: contractor.location,
            expertise: contractor.expertise,
            pricing: contractor.pricing,
            verification_status: contractor.verification_status,
        };

        res.json({ message: 'Profile updated', profile });
    } catch (error) {
        throw new ExpressError(`Update failed: ${error.message}`, 500);
    }
};

// Get service requests
exports.getServiceRequests = async (req, res) => {
    try {
        const contractor = await Contractor.findOne({ user_id: req.user.id });
        if (!contractor) throw new ExpressError('Contractor not found', 404);

        const cacheKey = `requests:${req.user.id}`;
        const cachedRequests = await client.get(cacheKey);

        if (cachedRequests) {
            return res.json({ requests: JSON.parse(cachedRequests) });
        }

        const requests = await ServiceRequest.find({
            contractor_id: contractor._id,
        }).populate('user_id');
        await client.setEx(cacheKey, 3600, JSON.stringify(requests)); // Cache for 1 hour in Redis
        res.json({ requests });
    } catch (error) {
        throw new ExpressError(
            `Failed to fetch requests: ${error.message}`,
            500
        );
    }
};

// Accept or decline a project offer
exports.respondToRequest = async (req, res) => {
    const { requestId } = req.params;
    const { status } = req.body; // 'accepted' or 'declined'
    try {
        const contractor = await Contractor.findOne({ user_id: req.user.id });
        if (!contractor) throw new ExpressError('Contractor not found', 404);

        const request = await ServiceRequest.findOneAndUpdate(
            { _id: requestId, contractor_id: contractor._id },
            { status },
            { new: true }
        ).populate('user_id');
        if (!request) throw new ExpressError('Request not found', 404);

        const cacheKey = `requests:${req.user.id}`;
        await client.del(cacheKey);
        res.json({ message: `Request ${status}`, request });
    } catch (error) {
        throw new ExpressError(
            `Failed to respond to request: ${error.message}`,
            500
        );
    }
};

// Get bookings (fetches from MongoDB)
exports.getBookings = async (req, res) => {
    try {
        const contractor = await Contractor.findOne({ user_id: req.user.id });
        if (!contractor) throw new ExpressError('Contractor not found', 404);

        const bookings = await Booking.find({
            contractor_id: contractor._id,
        }).populate('user_id');
        res.json({ bookings });
    } catch (error) {
        throw new ExpressError(
            `Failed to fetch bookings: ${error.message}`,
            500
        );
    }
};
