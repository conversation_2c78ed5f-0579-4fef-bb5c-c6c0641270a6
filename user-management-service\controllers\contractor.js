const ExpressError = require('@utils/ExpressError');
const User = require('../model/user');
const { client } = require('../cache');

// Register a new contractor
exports.contractorRegister = async (req, res) => {
    const { email, location, documents, specialties, portfolio, experience } = req.body;

    const user = await User.findOne({ email });
    if (!user) throw new ExpressError('User not found. Please sign up as a user first', 404);

    if (user.role !== 'user') {
        throw new ExpressError('User already registered with another role', 400);
    }

    user.role = 'contractor';
    user.location = location;
    user.documents = documents;
    user.specialties = specialties;
    user.portfolio = portfolio;
    user.experience = experience;

    try {
        await user.save();
        res.status(200).json({ message: 'Successfully registered as a contractor' });
    } catch (error) {
        throw new ExpressError(`Failed to register as contractor: ${error.message}`, 500);
    }
};


// Update contractor profile (updates MongoDB, invalidates Redis cache)
exports.updateContractorProfile = async (req, res) => {
    const { location, documents, specialties, portfolio, experience } = req.body;

    try {
        const user = await User.findById(req.user.id);
        if (!user || user.role !== 'contractor') {
            throw new ExpressError('Contractor not found', 404);
        }

        if (location !== undefined) user.location = location;
        if (documents !== undefined) user.documents = documents;
        if (specialties !== undefined) user.specialties = specialties;
        if (portfolio !== undefined) user.portfolio = portfolio;
        if (experience !== undefined) user.experience = experience;

        await user.save();

        const cacheKey = `contractor:${req.user.id}`;
        await client.del(cacheKey);

        res.json({ message: 'Profile updated successfully', updatedUser: user });
    } catch (error) {
        throw new ExpressError(`Update failed: ${error.message}`, 500);
    }
};

