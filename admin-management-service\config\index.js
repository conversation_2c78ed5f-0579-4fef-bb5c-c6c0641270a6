require('dotenv').config();

class EnvConfig {
    constructor() {
        this.requiredVars = {
            DATABASE_URL: process.env.DATABASE_URL,
            PORT: process.env.PORT || 3001,
        };

        this._validate();
    }

    _validate() {
        const missing = Object.entries(this.requiredVars)
            .filter(([_, value]) => !value)
            .map(([key]) => key);

        if (missing.length > 0) {
            console.error(
                `❌ Missing required environment variables:\n${missing.join('\n')}`
            );
            process.exit(1);
        }
    }

    get(key) {
        return this.requiredVars[key];
    }

    getAll() {
        return { ...this.requiredVars };
    }
}

const env = new EnvConfig();

module.exports = env;
