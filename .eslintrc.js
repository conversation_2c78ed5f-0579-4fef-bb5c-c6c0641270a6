const path = require('path');
const fs = require('fs');

const ROOT = __dirname;
const ALIAS_DIRS = ['utils'];

const resolverMap = [];
const coreModules = [];

function collectCoreModules(dirPath, aliasBase) {
    const results = [aliasBase]; // always whitelist the base alias
    const entries = fs.readdirSync(dirPath);

    for (const name of entries) {
        const fullPath = path.join(dirPath, name);
        const stat = fs.statSync(fullPath);

        if (stat.isFile() && path.extname(name) === '.js') {
            // file.js → '@alias/file'
            const modName = name.replace(/\.js$/, '');
            results.push(`${aliasBase}/${modName}`);
        }

        if (stat.isDirectory()) {
            // if there's an index.js, whitelist '@alias/dir' and '@alias/dir/index'
            const idxFile = path.join(fullPath, 'index.js');
            const subAlias = `${aliasBase}/${name}`;
            if (fs.existsSync(idxFile)) {
                results.push(subAlias, `${subAlias}/index`);
            }
            // then recurse into the subdirectory
            results.push(...collectCoreModules(fullPath, subAlias));
        }
    }

    return results;
}

for (const dir of ALIAS_DIRS) {
    const fullDir = path.resolve(ROOT, dir);
    const aliasBase = `@${dir}`;

    // only if the directory actually exists
    if (fs.existsSync(fullDir) && fs.statSync(fullDir).isDirectory()) {
        // two entries for alias resolution
        resolverMap.push([aliasBase, fullDir]);
        resolverMap.push([`${aliasBase}/(.*)`, path.join(fullDir, '$1')]);

        // collect every nested module under it
        coreModules.push(...collectCoreModules(fullDir, aliasBase));
    }
}

module.exports = {
    env: {
        browser: true,
        commonjs: true,
        es2021: true,
        node: true,
    },
    extends: ['airbnb', 'plugin:prettier/recommended'],
    overrides: [],
    parserOptions: {
        ecmaVersion: 'latest',
    },
    plugins: ['no-autofix', 'prettier'],
    rules: {
        'no-underscore-dangle': 0,
        'prettier/prettier': ['error'],
        'no-unused-vars': [1, { argsIgnorePattern: '^_' }],
        'prefer-const': 0,
        'no-autofix/prefer-const': 1,
        'func-names': [1, 'never'],
        'consistent-return': 0,
        'linebreak-style': 0,
    },
    settings: {
        'import/resolver': {
            alias: {
                map: resolverMap,
                extensions: ['.js', '.json'],
            },
        },
        'import/core-modules': coreModules,
    },
};
